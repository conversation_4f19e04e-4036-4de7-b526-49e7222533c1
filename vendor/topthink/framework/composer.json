{"name": "topthink/framework", "description": "The ThinkPHP Framework.", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.0.0", "ext-json": "*", "ext-mbstring": "*", "ext-ctype": "*", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "psr/http-message": "^1.0", "topthink/think-orm": "^3.0|^4.0", "topthink/think-helper": "^3.1", "topthink/think-container": "^3.0", "topthink/think-validate": "^3.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^9.5", "guzzlehttp/psr7": "^2.1.0"}, "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "autoload-dev": {"psr-4": {"think\\tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true}, "scripts": {"php-cs-fixer": "php-cs-fixer fix src/ --rules=@PER-CS2.0 --dry-run --diff"}}