name: build

on:
  push:
    branches:
      - "8.x"
  pull_request:
  schedule:
    - cron: "0 0 * * *"

jobs:
  tests:
    runs-on: ubuntu-22.04

    strategy:
      fail-fast: true
      matrix:
        php: [8.0, 8.1, 8.2, 8.3, 8.4]

    name: PHP ${{ matrix.php }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 10

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2
          coverage: xdebug

      - name: Install dependencies
        uses: nick-fields/retry@v2
        with:
          timeout_minutes: 5
          max_attempts: 5
          command: composer update --prefer-dist --no-interaction --prefer-stable --no-suggest

      - name: Execute tests
        run: vendor/bin/phpunit --coverage-clover build/logs/clover.xml

      - name: Upload Scrutinizer coverage
        uses: sudo-bot/action-scrutinizer@latest
        with:
          cli-args: "--format=php-clover build/logs/clover.xml --revision=${{ github.event.pull_request.head.sha || github.sha }}"
