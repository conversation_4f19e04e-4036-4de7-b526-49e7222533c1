{"packages": [{"name": "league/flysystem", "version": "1.1.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "time": "2022-10-04T09:16:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "install-path": "../league/flysystem"}, {"name": "league/flysystem-cached-adapter", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-cached-adapter.git", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-cached-adapter/zipball/d1925efb2207ac4be3ad0c40b8277175f99ffaff", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"league/flysystem": "~1.0", "psr/cache": "^1.0.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0", "tedivm/stash": "~0.12"}, "suggest": {"ext-phpredis": "Pure C implemented extension for PHP"}, "time": "2020-07-25T15:56:04+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\Cached\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "frank<PERSON><PERSON>e", "email": "<EMAIL>"}], "description": "An adapter decorator to enable meta-data caching.", "support": {"issues": "https://github.com/thephpleague/flysystem-cached-adapter/issues", "source": "https://github.com/thephpleague/flysystem-cached-adapter/tree/master"}, "install-path": "../league/flysystem-cached-adapter"}, {"name": "league/mime-type-detection", "version": "1.15.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "reference": "ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "time": "2024-01-28T23:22:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.15.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "install-path": "../league/mime-type-detection"}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "install-path": "../psr/cache"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "install-path": "../psr/container"}, {"name": "psr/http-message", "version": "1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:50:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "79dff0b268932c640297f5208d6298f71855c03e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/79dff0b268932c640297f5208d6298f71855c03e", "reference": "79dff0b268932c640297f5208d6298f71855c03e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0"}, "time": "2024-08-21T13:31:24+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.1"}, "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "install-path": "../psr/simple-cache"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:20:29+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/var-dumper", "version": "v7.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "548f6760c54197b1084e1e5c71f6d9d523f2f78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/548f6760c54197b1084e1e5c71f6d9d523f2f78e", "reference": "548f6760c54197b1084e1e5c71f6d9d523f2f78e", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "time": "2025-04-27T18:39:23+00:00", "bin": ["Resources/bin/var-dump-server"], "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/var-dumper"}, {"name": "topthink/framework", "version": "v8.1.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "8faec5c9b7a7f2a66ca3140a57e81bd6cd37567c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/8faec5c9b7a7f2a66ca3140a57e81bd6cd37567c", "reference": "8faec5c9b7a7f2a66ca3140a57e81bd6cd37567c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=8.0.0", "psr/http-message": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "topthink/think-container": "^3.0", "topthink/think-helper": "^3.1", "topthink/think-orm": "^3.0|^4.0", "topthink/think-validate": "^3.0"}, "require-dev": {"guzzlehttp/psr7": "^2.1.0", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^9.5"}, "time": "2025-01-14T08:04:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/v8.1.2"}, "install-path": "../topthink/framework"}, {"name": "topthink/think-captcha", "version": "v3.0.11", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "4f24f560a31011329e3d144732e5370d7676b3fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/4f24f560a31011329e3d144732e5370d7676b3fb", "reference": "4f24f560a31011329e3d144732e5370d7676b3fb", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"topthink/framework": "^6.0|^8.0"}, "time": "2024-11-22T12:59:35+00:00", "type": "library", "extra": {"think": {"config": {"captcha": "src/config.php"}, "services": ["think\\captcha\\CaptchaService"]}}, "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\captcha\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp", "support": {"issues": "https://github.com/top-think/think-captcha/issues", "source": "https://github.com/top-think/think-captcha/tree/v3.0.11"}, "install-path": "../topthink/think-captcha"}, {"name": "topthink/think-container", "version": "v3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-container.git", "reference": "a24d442a02fb2a4716de232ff1a4f006c178a370"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-container/zipball/a24d442a02fb2a4716de232ff1a4f006c178a370", "reference": "a24d442a02fb2a4716de232ff1a4f006c178a370", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0", "psr/container": "^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2025-01-07T08:19:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": [], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "PHP Container & Facade Manager", "support": {"issues": "https://github.com/top-think/think-container/issues", "source": "https://github.com/top-think/think-container/tree/v3.0.1"}, "install-path": "../topthink/think-container"}, {"name": "topthink/think-dumper", "version": "v1.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-dumper.git", "reference": "eba662a1843d5db68059050c530f7d43287289fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-dumper/zipball/eba662a1843d5db68059050c530f7d43287289fc", "reference": "eba662a1843d5db68059050c530f7d43287289fc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.2", "symfony/var-dumper": ">=6.0", "topthink/framework": "^6.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^11.4"}, "time": "2025-03-21T07:15:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\dumper\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Dumper extend for thinkphp", "support": {"issues": "https://github.com/top-think/think-dumper/issues", "source": "https://github.com/top-think/think-dumper/tree/v1.0.5"}, "install-path": "../topthink/think-dumper"}, {"name": "topthink/think-filesystem", "version": "v2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-filesystem.git", "reference": "e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-filesystem/zipball/e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d", "reference": "e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"league/flysystem": "^1.1.4", "league/flysystem-cached-adapter": "^1.0", "php": ">=7.2.5", "topthink/framework": "^6.1|^8.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^8.0"}, "time": "2024-10-16T03:37:24+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6.1 Filesystem Package", "support": {"issues": "https://github.com/top-think/think-filesystem/issues", "source": "https://github.com/top-think/think-filesystem/tree/v2.0.3"}, "install-path": "../topthink/think-filesystem"}, {"name": "topthink/think-helper", "version": "v3.1.11", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "reference": "1d6ada9b9f3130046bf6922fe1bd159c8d88a33c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "time": "2025-04-07T06:55:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.11"}, "install-path": "../topthink/think-helper"}, {"name": "topthink/think-multi-app", "version": "v1.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-multi-app.git", "reference": "f93c604d5cfac2b613756273224ee2f88e457b88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-multi-app/zipball/f93c604d5cfac2b613756273224ee2f88e457b88", "reference": "f93c604d5cfac2b613756273224ee2f88e457b88", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0|^8.0"}, "time": "2024-11-25T08:52:44+00:00", "type": "library", "extra": {"think": {"services": ["think\\app\\Service"]}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\app\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp multi app support", "support": {"issues": "https://github.com/top-think/think-multi-app/issues", "source": "https://github.com/top-think/think-multi-app/tree/v1.1.1"}, "install-path": "../topthink/think-multi-app"}, {"name": "topthink/think-orm", "version": "v4.0.40", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "1637860ff736859058f0a5003c7cc719c2068dbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/1637860ff736859058f0a5003c7cc719c2068dbc", "reference": "1637860ff736859058f0a5003c7cc719c2068dbc", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=8.0.0", "psr/log": ">=1.0", "psr/simple-cache": ">=1.0", "topthink/think-helper": "^3.1", "topthink/think-validate": "^3.0"}, "require-dev": {"phpunit/phpunit": "^9.6|^10"}, "suggest": {"ext-mongodb": "provide mongodb support"}, "time": "2025-05-27T03:32:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php", "stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the PHP Database&ORM Framework", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/top-think/think-orm/issues", "source": "https://github.com/top-think/think-orm/tree/v4.0.40"}, "install-path": "../topthink/think-orm"}, {"name": "topthink/think-template", "version": "v3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-template.git", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-template/zipball/0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0", "psr/simple-cache": ">=1.0"}, "time": "2024-10-16T03:41:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "support": {"issues": "https://github.com/top-think/think-template/issues", "source": "https://github.com/top-think/think-template/tree/v3.0.2"}, "install-path": "../topthink/think-template"}, {"name": "topthink/think-trace", "version": "v1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-trace.git", "reference": "136cd5d97e8bdb780e4b5c1637c588ed7ca3e142"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-trace/zipball/136cd5d97e8bdb780e4b5c1637c588ed7ca3e142", "reference": "136cd5d97e8bdb780e4b5c1637c588ed7ca3e142", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0|^8.0"}, "time": "2023-02-07T08:36:32+00:00", "type": "library", "extra": {"think": {"config": {"trace": "src/config.php"}, "services": ["think\\trace\\Service"]}}, "installation-source": "dist", "autoload": {"psr-4": {"think\\trace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp debug trace", "support": {"issues": "https://github.com/top-think/think-trace/issues", "source": "https://github.com/top-think/think-trace/tree/v1.6"}, "install-path": "../topthink/think-trace"}, {"name": "topthink/think-validate", "version": "v3.0.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-validate.git", "reference": "85063f6d4ef8ed122f17a36179dc3e0949b30988"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-validate/zipball/85063f6d4ef8ed122f17a36179dc3e0949b30988", "reference": "85063f6d4ef8ed122f17a36179dc3e0949b30988", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0", "topthink/think-container": ">=3.0"}, "time": "2025-06-11T05:51:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think validate", "support": {"issues": "https://github.com/top-think/think-validate/issues", "source": "https://github.com/top-think/think-validate/tree/v3.0.7"}, "install-path": "../topthink/think-validate"}, {"name": "topthink/think-view", "version": "v2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/top-think/think-view.git", "reference": "d2a076011c96d2edd8016703a827fb54b2683c62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-view/zipball/d2a076011c96d2edd8016703a827fb54b2683c62", "reference": "d2a076011c96d2edd8016703a827fb54b2683c62", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=8.0.0", "topthink/think-template": "^3.0"}, "time": "2023-02-25T12:18:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "support": {"issues": "https://github.com/top-think/think-view/issues", "source": "https://github.com/top-think/think-view/tree/v2.0.0"}, "install-path": "../topthink/think-view"}], "dev": true, "dev-package-names": ["symfony/deprecation-contracts", "symfony/polyfill-mbstring", "symfony/var-dumper", "topthink/think-dumper", "topthink/think-trace"]}