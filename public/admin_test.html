<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员系统接口测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .result { margin-top: 15px; padding: 15px; border-radius: 4px; white-space: pre-wrap; font-family: monospace; }
        .result.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .user-info { background-color: #e7f3ff; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        @media (max-width: 768px) { .grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 管理员系统接口测试</h1>
        
        <!-- 当前登录状态 -->
        <div class="section">
            <h2>当前状态</h2>
            <div id="userInfo" class="user-info">未登录</div>
            <button class="btn btn-primary" onclick="checkLoginStatus()">检查登录状态</button>
            <button class="btn btn-success" onclick="getUserInfo()">获取用户信息</button>
        </div>

        <div class="grid">
            <!-- 登录测试 -->
            <div class="section">
                <h2>🔑 登录测试</h2>
                <div class="form-group">
                    <label>用户名:</label>
                    <input type="text" id="loginUsername" value="admin" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" id="loginPassword" value="123456" placeholder="请输入密码">
                </div>
                <button class="btn btn-primary" onclick="login()">登录</button>
                <button class="btn btn-danger" onclick="logout()">登出</button>
                <div id="loginResult" class="result" style="display:none;"></div>
            </div>

            <!-- 管理员管理测试 -->
            <div class="section">
                <h2>👥 管理员管理</h2>
                <button class="btn btn-primary" onclick="getAdminList()">获取管理员列表</button>
                <button class="btn btn-success" onclick="showCreateForm()">显示创建表单</button>
                
                <div id="createAdminForm" style="display:none; margin-top:15px;">
                    <h3>创建管理员</h3>
                    <div class="form-group">
                        <label>用户名:</label>
                        <input type="text" id="createUsername" placeholder="请输入用户名">
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" id="createPassword" placeholder="请输入密码">
                    </div>
                    <div class="form-group">
                        <label>邮箱:</label>
                        <input type="email" id="createEmail" placeholder="请输入邮箱">
                    </div>
                    <div class="form-group">
                        <label>真实姓名:</label>
                        <input type="text" id="createRealName" placeholder="请输入真实姓名">
                    </div>
                    <div class="form-group">
                        <label>角色:</label>
                        <select id="createRole">
                            <option value="content_admin">内容管理员</option>
                            <option value="super_admin">超级管理员</option>
                        </select>
                    </div>
                    <button class="btn btn-success" onclick="createAdmin()">创建</button>
                    <button class="btn btn-warning" onclick="hideCreateForm()">取消</button>
                </div>
                
                <div id="adminResult" class="result" style="display:none;"></div>
            </div>
        </div>

        <!-- 权限测试 -->
        <div class="section">
            <h2>🛡️ 权限测试</h2>
            <p>以下操作需要不同的权限级别，可以测试权限控制是否正常工作：</p>
            <button class="btn btn-primary" onclick="testContentAdminPermission()">测试内容管理员权限</button>
            <button class="btn btn-danger" onclick="testSuperAdminPermission()">测试超级管理员权限</button>
            <div id="permissionResult" class="result" style="display:none;"></div>
        </div>

        <!-- 接口文档 -->
        <div class="section">
            <h2>📖 接口文档</h2>
            <h3>认证接口</h3>
            <ul>
                <li><strong>POST /admin/auth/login</strong> - 管理员登录</li>
                <li><strong>POST /admin/auth/logout</strong> - 管理员登出</li>
                <li><strong>GET /admin/auth/info</strong> - 获取当前用户信息</li>
                <li><strong>GET /admin/auth/check</strong> - 检查登录状态</li>
            </ul>
            <h3>管理员管理接口（需要超级管理员权限）</h3>
            <ul>
                <li><strong>GET /admin/admin/index</strong> - 管理员列表</li>
                <li><strong>POST /admin/admin/create</strong> - 创建管理员</li>
                <li><strong>PUT /admin/admin/update/{id}</strong> - 更新管理员</li>
                <li><strong>DELETE /admin/admin/delete/{id}</strong> - 删除管理员</li>
                <li><strong>GET /admin/admin/read/{id}</strong> - 获取管理员详情</li>
            </ul>
            <h3>默认账号</h3>
            <ul>
                <li><strong>超级管理员:</strong> admin / 123456</li>
                <li><strong>内容管理员:</strong> editor / 123456</li>
            </ul>
        </div>
    </div>

    <script>
        const baseUrl = window.location.origin;

        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }

        // 发送请求
        async function sendRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                };

                if (data) {
                    if (method === 'GET') {
                        url += '?' + new URLSearchParams(data).toString();
                    } else {
                        options.body = JSON.stringify(data);
                    }
                }

                const response = await fetch(url, options);
                const result = await response.json();
                return { success: response.ok, data: result };
            } catch (error) {
                return { success: false, data: { message: error.message } };
            }
        }

        // 检查登录状态
        async function checkLoginStatus() {
            const result = await sendRequest(`${baseUrl}/admin/auth/check`);
            showResult('userInfo', result.data, result.success);
            if (result.success && result.data.data.is_login) {
                getUserInfo();
            }
        }

        // 获取用户信息
        async function getUserInfo() {
            const result = await sendRequest(`${baseUrl}/admin/auth/info`);
            if (result.success) {
                const userInfo = result.data.data;
                document.getElementById('userInfo').innerHTML = `
                    <strong>已登录:</strong> ${userInfo.username} (${userInfo.real_name || '未设置'})
                    <br><strong>角色:</strong> ${userInfo.role === 'super_admin' ? '超级管理员' : '内容管理员'}
                    <br><strong>邮箱:</strong> ${userInfo.email || '未设置'}
                `;
            }
        }

        // 登录
        async function login() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            if (!username || !password) {
                showResult('loginResult', '请输入用户名和密码', false);
                return;
            }

            const result = await sendRequest(`${baseUrl}/admin/auth/login`, 'POST', {
                username: username,
                password: password
            });

            showResult('loginResult', result.data, result.success);

            if (result.success) {
                setTimeout(() => {
                    getUserInfo();
                }, 500);
            }
        }

        // 登出
        async function logout() {
            const result = await sendRequest(`${baseUrl}/admin/auth/logout`, 'POST');
            showResult('loginResult', result.data, result.success);

            if (result.success) {
                document.getElementById('userInfo').textContent = '未登录';
            }
        }

        // 获取管理员列表
        async function getAdminList() {
            const result = await sendRequest(`${baseUrl}/admin/admin/index`);
            showResult('adminResult', result.data, result.success);
        }

        // 显示创建表单
        function showCreateForm() {
            document.getElementById('createAdminForm').style.display = 'block';
        }

        // 隐藏创建表单
        function hideCreateForm() {
            document.getElementById('createAdminForm').style.display = 'none';
        }

        // 创建管理员
        async function createAdmin() {
            const data = {
                username: document.getElementById('createUsername').value,
                password: document.getElementById('createPassword').value,
                email: document.getElementById('createEmail').value,
                real_name: document.getElementById('createRealName').value,
                role: document.getElementById('createRole').value,
                status: 1
            };

            if (!data.username || !data.password) {
                showResult('adminResult', '用户名和密码不能为空', false);
                return;
            }

            const result = await sendRequest(`${baseUrl}/admin/admin/create`, 'POST', data);
            showResult('adminResult', result.data, result.success);

            if (result.success) {
                hideCreateForm();
                // 清空表单
                document.getElementById('createUsername').value = '';
                document.getElementById('createPassword').value = '';
                document.getElementById('createEmail').value = '';
                document.getElementById('createRealName').value = '';
            }
        }

        // 测试内容管理员权限
        async function testContentAdminPermission() {
            showResult('permissionResult', '内容管理员权限测试：尝试访问用户信息接口...', true);
            const result = await sendRequest(`${baseUrl}/admin/auth/info`);
            showResult('permissionResult', `内容管理员权限测试结果：\n${JSON.stringify(result.data, null, 2)}`, result.success);
        }

        // 测试超级管理员权限
        async function testSuperAdminPermission() {
            showResult('permissionResult', '超级管理员权限测试：尝试访问管理员列表...', true);
            const result = await sendRequest(`${baseUrl}/admin/admin/index`);
            showResult('permissionResult', `超级管理员权限测试结果：\n${JSON.stringify(result.data, null, 2)}`, result.success);
        }

        // 页面加载时检查登录状态
        window.onload = function() {
            checkLoginStatus();
        };
    </script>
</body>
</html>
