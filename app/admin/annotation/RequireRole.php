<?php
declare(strict_types=1);

namespace app\admin\annotation;

use Attribute;

/**
 * 角色权限注解
 * 
 * 使用示例：
 * #[RequireRole('super_admin')]
 * #[RequireRole(['super_admin', 'content_admin'])]
 */
#[Attribute(Attribute::TARGET_METHOD | Attribute::TARGET_CLASS)]
class RequireRole
{
    /**
     * 需要的角色
     * @var array|string
     */
    public array|string $roles;
    
    /**
     * 错误消息
     * @var string
     */
    public string $message;
    
    /**
     * 构造函数
     * 
     * @param array|string $roles 需要的角色，可以是字符串或数组
     * @param string $message 权限不足时的错误消息
     */
    public function __construct(array|string $roles, string $message = '权限不足')
    {
        $this->roles = is_string($roles) ? [$roles] : $roles;
        $this->message = $message;
    }
    
    /**
     * 获取需要的角色列表
     */
    public function getRoles(): array
    {
        return is_array($this->roles) ? $this->roles : [$this->roles];
    }
    
    /**
     * 获取错误消息
     */
    public function getMessage(): string
    {
        return $this->message;
    }
}
