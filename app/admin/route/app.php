<?php
// admin应用路由定义文件

use think\facade\Route;

// 认证相关路由
Route::group('auth', function () {
    Route::post('login', 'Auth/login');           // 登录
    Route::post('logout', 'Auth/logout');         // 登出
    Route::get('info', 'Auth/info');              // 获取用户信息
    Route::get('check', 'Auth/check');            // 检查登录状态
});

// 管理员管理路由
Route::group('admin', function () {
    Route::get('index', 'Index/index');           // 管理员列表
    Route::post('create', 'Index/create');        // 创建管理员
    Route::put('update/:id', 'Index/update');     // 更新管理员
    Route::delete('delete/:id', 'Index/delete');  // 删除管理员
    Route::get('read/:id', 'Index/read');         // 获取管理员详情
});

// 设置跨域
Route::header('Access-Control-Allow-Origin', '*');
Route::header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
Route::header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
