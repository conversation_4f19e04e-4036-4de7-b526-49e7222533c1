<?php
declare(strict_types=1);

namespace app\admin\middleware;

use app\admin\service\AuthService;
use app\admin\annotation\RequireRole;
use think\Request;
use think\Response;
use Closure;
use ReflectionClass;
use ReflectionMethod;

/**
 * 角色权限中间件
 */
class RoleMiddleware
{
    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 获取当前路由信息
        $pathInfo = trim($request->pathinfo(), '/');

        // 排除不需要权限验证的路由
        $excludeRoutes = [
            'auth/login',
            'auth/logout',
            'auth/info',
            'auth/check'
        ];

        // 检查是否匹配排除路由
        foreach ($excludeRoutes as $excludeRoute) {
            if ($pathInfo === $excludeRoute || str_ends_with($pathInfo, '/' . $excludeRoute)) {
                return $next($request);
            }
        }

        // 获取控制器和方法
        $controller = $request->controller();
        $action = $request->action();

        // 如果获取不到，尝试从路由信息中解析
        if (empty($controller) || empty($action)) {
            $pathInfo = trim($request->pathinfo(), '/');
            $pathParts = explode('/', $pathInfo);

            // 调试信息
            if (app()->isDebug()) {
                trace("RoleMiddleware - PathInfo: {$pathInfo}, PathParts: " . json_encode($pathParts));
            }

            if (count($pathParts) >= 2) {
                $controller = $pathParts[count($pathParts) - 2];
                $action = $pathParts[count($pathParts) - 1];

                // 特殊处理：admin/index -> Index/index (admin应用中的admin路由组对应Index控制器)
                if ($controller === 'admin') {
                    $controller = 'Index';
                }
            }
        }

        $authService = new AuthService();

        // 检查权限注解
        $requiredRoles = $this->getRequiredRoles($controller, $action);

        // 调试信息
        if (app()->isDebug()) {
            trace("RoleMiddleware - Controller: {$controller}, Action: {$action}, Required Roles: " . json_encode($requiredRoles));
        }

        if (!empty($requiredRoles)) {
            // 检查用户角色
            if (!$authService->hasAnyRole($requiredRoles)) {
                return json([
                    'code' => 403,
                    'message' => '权限不足，需要角色：' . implode('、', $this->translateRoles($requiredRoles)),
                    'data' => null
                ], 403);
            }
        }

        return $next($request);
    }
    
    /**
     * 获取需要的角色
     */
    private function getRequiredRoles(string $controller, string $action): array
    {
        try {
            $controllerClass = "app\\admin\\controller\\{$controller}";

            if (!class_exists($controllerClass)) {
                if (app()->isDebug()) {
                    trace("RoleMiddleware - Controller class not found: {$controllerClass}");
                }
                return [];
            }

            $reflectionClass = new ReflectionClass($controllerClass);

            // 检查方法注解
            if ($reflectionClass->hasMethod($action)) {
                $reflectionMethod = new ReflectionMethod($controllerClass, $action);
                $attributes = $reflectionMethod->getAttributes(RequireRole::class);

                if (app()->isDebug()) {
                    trace("RoleMiddleware - Found " . count($attributes) . " RequireRole attributes for {$controllerClass}::{$action}");
                }

                if (!empty($attributes)) {
                    $requireRole = $attributes[0]->newInstance();
                    return $requireRole->getRoles();
                }
            } else {
                if (app()->isDebug()) {
                    trace("RoleMiddleware - Method {$action} not found in {$controllerClass}");
                }
            }
            
            // 检查类注解
            $classAttributes = $reflectionClass->getAttributes(RequireRole::class);
            if (!empty($classAttributes)) {
                $requireRole = $classAttributes[0]->newInstance();
                return $requireRole->getRoles();
            }
            
        } catch (\Exception $e) {
            // 忽略反射异常
        }
        
        return [];
    }
    
    /**
     * 翻译角色名称
     */
    private function translateRoles(array $roles): array
    {
        $roleMap = [
            'super_admin' => '超级管理员',
            'content_admin' => '内容管理员',
        ];
        
        return array_map(function($role) use ($roleMap) {
            return $roleMap[$role] ?? $role;
        }, $roles);
    }
}
